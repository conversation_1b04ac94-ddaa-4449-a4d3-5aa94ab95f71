# 🐍 贪吃蛇游戏

一个使用现代 Web 技术栈开发的增强版贪吃蛇游戏。

## 🎮 游戏特性

### 基本功能
- **流畅的游戏体验**: 使用 `requestAnimationFrame` 实现60FPS流畅动画
- **响应式设计**: 支持桌面和移动设备
- **触摸控制**: 移动设备支持滑动手势控制
- **本地存储**: 自动保存最高分和游戏统计

### 增强功能
- **多种食物类型**:
  - 🍎 普通食物 (+10分)
  - ⭐ 奖励食物 (+25分)
  - 💎 超级食物 (+50分)

- **等级系统**: 每吃5个食物升一级
- **障碍物系统**: 高等级会出现障碍物增加难度
- **粒子效果**: 吃食物和游戏结束时的视觉特效
- **音效系统**: 使用 Web Audio API 生成游戏音效

### 视觉效果
- **渐变蛇身**: 蛇头到蛇尾的渐变透明度效果
- **动画食物**: 食物有呼吸动画效果
- **现代UI**: 圆角设计、阴影效果、渐变背景
- **升级提示**: 升级时的动画提示效果

## 🎯 游戏控制

### 桌面端
- **方向键**: 控制蛇的移动方向
- **空格键**: 暂停/继续游戏
- **按钮**: 点击界面按钮控制游戏

### 移动端
- **滑动手势**: 在游戏区域滑动控制方向
- **触摸按钮**: 点击控制按钮

## 🚀 技术栈

- **HTML5**: 游戏结构和Canvas绘图
- **CSS3**: 现代化样式和动画效果
- **JavaScript ES6+**: 游戏逻辑和类结构
- **Canvas API**: 游戏渲染
- **Web Audio API**: 音效生成
- **LocalStorage**: 数据持久化

## 📁 文件结构

```
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # 游戏逻辑
└── README.md           # 说明文档
```

## 🎲 游戏规则

1. 控制蛇吃食物来获得分数和增长身体
2. 不同颜色的食物有不同的分数值
3. 每吃5个食物升一级，游戏难度增加
4. 避免撞到墙壁、自己的身体或障碍物
5. 游戏速度会随着分数增加而提升

## 🏆 计分系统

- 普通食物: 10分
- 奖励食物: 25分  
- 超级食物: 50分
- 等级奖励: 每升一级额外获得成就感

## 🔧 运行方法

1. 下载所有文件到同一目录
2. 在浏览器中打开 `index.html`
3. 点击"开始游戏"或按空格键开始
4. 享受游戏！

## 🌟 优化特性

- **性能优化**: 使用 requestAnimationFrame 替代 setInterval
- **内存管理**: 及时清理粒子效果和事件监听器
- **错误处理**: 音频API的兼容性处理
- **用户体验**: 平滑的方向切换和碰撞检测

## 🎨 自定义配置

游戏配置可以通过修改 `script.js` 中的 `GAME_CONFIG` 对象来调整：

```javascript
const GAME_CONFIG = {
    GRID_SIZE: 20,              // 网格大小
    INITIAL_SPEED: 150,         // 初始速度
    MIN_SPEED: 80,              // 最小速度
    SPEED_INCREMENT: 2,         // 速度增量
    FOOD_TYPES: { ... },        // 食物类型配置
    COLORS: { ... }             // 颜色配置
};
```

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进游戏！

## 📄 许可证

MIT License - 可自由使用和修改
