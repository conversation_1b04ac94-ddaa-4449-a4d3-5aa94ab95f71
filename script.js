// 游戏配置
const GAME_CONFIG = {
    GRID_SIZE: 20,
    INITIAL_SPEED: 150,
    MIN_SPEED: 80,
    SPEED_INCREMENT: 2,
    FOOD_TYPES: {
        NORMAL: { score: 10, color: '#f56565', emoji: '🍎' },
        BONUS: { score: 25, color: '#ffd700', emoji: '⭐' },
        SUPER: { score: 50, color: '#9f7aea', emoji: '💎' }
    },
    COLORS: {
        SNAKE_HEAD: '#38a169',
        SNAKE_BODY: '#48bb78',
        BACKGROUND: '#2d3748',
        OBSTACLE: '#e53e3e'
    }
};

// 工具函数
const Utils = {
    randomInt: (min, max) => Math.floor(Math.random() * (max - min + 1)) + min,

    distance: (pos1, pos2) => Math.abs(pos1.x - pos2.x) + Math.abs(pos1.y - pos2.y),

    createParticle: (x, y, color) => ({
        x, y, color,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        life: 30,
        maxLife: 30
    })
};

class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridSize = GAME_CONFIG.GRID_SIZE;
        this.tileCount = this.canvas.width / this.gridSize;

        // 游戏状态
        this.gameRunning = false;
        this.gamePaused = false;
        this.gameLoop = null;
        this.level = 1;
        this.foodEaten = 0;

        // 分数和统计
        this.score = 0;
        this.highScore = parseInt(localStorage.getItem('snakeHighScore')) || 0;
        this.gamesPlayed = parseInt(localStorage.getItem('snakeGamesPlayed')) || 0;

        // 蛇的初始状态
        this.snake = [
            {x: 10, y: 10}
        ];
        this.dx = 0;
        this.dy = 0;
        this.nextDirection = { dx: 0, dy: 0 };

        // 食物和障碍物
        this.food = this.generateFood();
        this.obstacles = [];
        this.particles = [];

        // 游戏速度和难度
        this.gameSpeed = GAME_CONFIG.INITIAL_SPEED;
        this.difficulty = 'normal';

        // 音效（模拟）
        this.soundEnabled = true;

        // 绑定事件
        this.bindEvents();
        this.updateDisplay();
        this.setupTouchControls();
    }
    
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.togglePause();
            } else if (this.gameRunning && !this.gamePaused) {
                this.changeDirection(e);
            }
        });

        // 按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });

        document.getElementById('pauseButton').addEventListener('click', () => {
            this.togglePause();
        });

        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
    }

    setupTouchControls() {
        let touchStartX = 0;
        let touchStartY = 0;

        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
        });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (!this.gameRunning || this.gamePaused) return;

            const touch = e.changedTouches[0];
            const deltaX = touch.clientX - touchStartX;
            const deltaY = touch.clientY - touchStartY;
            const minSwipeDistance = 30;

            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // 水平滑动
                if (Math.abs(deltaX) > minSwipeDistance) {
                    if (deltaX > 0 && this.dx !== -1) {
                        this.nextDirection = { dx: 1, dy: 0 };
                    } else if (deltaX < 0 && this.dx !== 1) {
                        this.nextDirection = { dx: -1, dy: 0 };
                    }
                }
            } else {
                // 垂直滑动
                if (Math.abs(deltaY) > minSwipeDistance) {
                    if (deltaY > 0 && this.dy !== -1) {
                        this.nextDirection = { dx: 0, dy: 1 };
                    } else if (deltaY < 0 && this.dy !== 1) {
                        this.nextDirection = { dx: 0, dy: -1 };
                    }
                }
            }
        });
    }
    
    changeDirection(event) {
        const keyPressed = event.code;

        if (keyPressed === 'ArrowLeft' && this.dx !== 1) {
            this.nextDirection = { dx: -1, dy: 0 };
        }
        if (keyPressed === 'ArrowUp' && this.dy !== 1) {
            this.nextDirection = { dx: 0, dy: -1 };
        }
        if (keyPressed === 'ArrowRight' && this.dx !== -1) {
            this.nextDirection = { dx: 1, dy: 0 };
        }
        if (keyPressed === 'ArrowDown' && this.dy !== -1) {
            this.nextDirection = { dx: 0, dy: 1 };
        }
    }

    generateFood() {
        let foodPosition;
        let attempts = 0;
        const maxAttempts = 100;

        do {
            foodPosition = {
                x: Utils.randomInt(0, this.tileCount - 1),
                y: Utils.randomInt(0, this.tileCount - 1)
            };
            attempts++;
        } while (this.isPositionOccupied(foodPosition) && attempts < maxAttempts);

        // 确定食物类型
        const rand = Math.random();
        let foodType;
        if (rand < 0.7) {
            foodType = 'NORMAL';
        } else if (rand < 0.9) {
            foodType = 'BONUS';
        } else {
            foodType = 'SUPER';
        }

        return {
            ...foodPosition,
            type: foodType,
            ...GAME_CONFIG.FOOD_TYPES[foodType],
            animationFrame: 0
        };
    }

    isPositionOccupied(pos) {
        // 检查是否与蛇身重叠
        for (let segment of this.snake) {
            if (segment.x === pos.x && segment.y === pos.y) {
                return true;
            }
        }

        // 检查是否与障碍物重叠
        for (let obstacle of this.obstacles) {
            if (obstacle.x === pos.x && obstacle.y === pos.y) {
                return true;
            }
        }

        return false;
    }

    generateObstacles() {
        this.obstacles = [];
        const obstacleCount = Math.floor(this.level / 3);

        for (let i = 0; i < obstacleCount; i++) {
            let obstaclePos;
            let attempts = 0;

            do {
                obstaclePos = {
                    x: Utils.randomInt(2, this.tileCount - 3),
                    y: Utils.randomInt(2, this.tileCount - 3)
                };
                attempts++;
            } while (this.isPositionOccupied(obstaclePos) && attempts < 50);

            if (attempts < 50) {
                this.obstacles.push(obstaclePos);
            }
        }
    }
    
    drawGame() {
        // 清空画布
        this.ctx.fillStyle = GAME_CONFIG.COLORS.BACKGROUND;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格（可选）
        this.drawGrid();

        // 绘制障碍物
        this.drawObstacles();

        // 绘制蛇
        this.drawSnake();

        // 绘制食物
        this.drawFood();

        // 绘制粒子效果
        this.drawParticles();
    }

    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        for (let i = 0; i <= this.tileCount; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(i * this.gridSize, 0);
            this.ctx.lineTo(i * this.gridSize, this.canvas.height);
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(0, i * this.gridSize);
            this.ctx.lineTo(this.canvas.width, i * this.gridSize);
            this.ctx.stroke();
        }
    }

    drawSnake() {
        this.snake.forEach((segment, index) => {
            const x = segment.x * this.gridSize;
            const y = segment.y * this.gridSize;

            if (index === 0) {
                // 蛇头 - 添加渐变效果
                const gradient = this.ctx.createRadialGradient(
                    x + this.gridSize/2, y + this.gridSize/2, 0,
                    x + this.gridSize/2, y + this.gridSize/2, this.gridSize/2
                );
                gradient.addColorStop(0, GAME_CONFIG.COLORS.SNAKE_HEAD);
                gradient.addColorStop(1, '#2f855a');
                this.ctx.fillStyle = gradient;

                // 绘制圆形蛇头
                this.ctx.beginPath();
                this.ctx.arc(
                    x + this.gridSize/2,
                    y + this.gridSize/2,
                    this.gridSize/2 - 1,
                    0, 2 * Math.PI
                );
                this.ctx.fill();

                // 绘制眼睛
                this.ctx.fillStyle = 'white';
                this.ctx.beginPath();
                this.ctx.arc(x + 6, y + 6, 2, 0, 2 * Math.PI);
                this.ctx.arc(x + 14, y + 6, 2, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = 'black';
                this.ctx.beginPath();
                this.ctx.arc(x + 6, y + 6, 1, 0, 2 * Math.PI);
                this.ctx.arc(x + 14, y + 6, 1, 0, 2 * Math.PI);
                this.ctx.fill();
            } else {
                // 蛇身 - 渐变效果
                const alpha = 1 - (index / this.snake.length) * 0.3;
                this.ctx.fillStyle = `rgba(72, 187, 120, ${alpha})`;

                this.ctx.fillRect(
                    x + 1, y + 1,
                    this.gridSize - 2, this.gridSize - 2
                );

                // 添加高光效果
                this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.3})`;
                this.ctx.fillRect(
                    x + 2, y + 2,
                    this.gridSize - 6, 3
                );
            }
        });
    }

    drawFood() {
        const x = this.food.x * this.gridSize;
        const y = this.food.y * this.gridSize;

        // 食物动画
        this.food.animationFrame = (this.food.animationFrame + 0.2) % (2 * Math.PI);
        const scale = 1 + Math.sin(this.food.animationFrame) * 0.1;
        const size = (this.gridSize - 4) * scale;
        const offset = (this.gridSize - size) / 2;

        // 绘制食物阴影
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(x + 2, y + 2, this.gridSize - 2, this.gridSize - 2);

        // 绘制食物
        this.ctx.fillStyle = this.food.color;
        this.ctx.fillRect(x + offset, y + offset, size, size);

        // 添加光泽效果
        const gradient = this.ctx.createLinearGradient(x, y, x + this.gridSize, y + this.gridSize);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(x + offset, y + offset, size/2, size/2);
    }

    drawObstacles() {
        this.ctx.fillStyle = GAME_CONFIG.COLORS.OBSTACLE;
        this.obstacles.forEach(obstacle => {
            const x = obstacle.x * this.gridSize;
            const y = obstacle.y * this.gridSize;

            this.ctx.fillRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2);

            // 添加纹理效果
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            this.ctx.fillRect(x + 1, y + 1, this.gridSize - 2, 3);
            this.ctx.fillRect(x + 1, y + 1, 3, this.gridSize - 2);

            this.ctx.fillStyle = GAME_CONFIG.COLORS.OBSTACLE;
        });
    }

    drawParticles() {
        this.particles.forEach((particle, index) => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life--;

            if (particle.life <= 0) {
                this.particles.splice(index, 1);
                return;
            }

            const alpha = particle.life / particle.maxLife;
            this.ctx.fillStyle = `rgba(${particle.color}, ${alpha})`;
            this.ctx.fillRect(particle.x, particle.y, 3, 3);
        });
    }
    
    moveSnake() {
        // 应用下一个方向
        if (this.nextDirection.dx !== 0 || this.nextDirection.dy !== 0) {
            this.dx = this.nextDirection.dx;
            this.dy = this.nextDirection.dy;
            this.nextDirection = { dx: 0, dy: 0 };
        }

        const head = {x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy};
        this.snake.unshift(head);

        // 检查是否吃到食物
        if (head.x === this.food.x && head.y === this.food.y) {
            this.eatFood();
        } else {
            this.snake.pop();
        }
    }

    eatFood() {
        const foodScore = this.food.score;
        this.score += foodScore;
        this.foodEaten++;

        // 创建粒子效果
        this.createFoodParticles();

        // 播放音效（模拟）
        this.playSound('eat');

        // 检查是否升级
        if (this.foodEaten % 5 === 0) {
            this.levelUp();
        }

        // 生成新食物
        this.food = this.generateFood();
        this.updateDisplay();

        // 增加游戏速度
        if (this.gameSpeed > GAME_CONFIG.MIN_SPEED) {
            this.gameSpeed -= GAME_CONFIG.SPEED_INCREMENT;
        }
    }

    createFoodParticles() {
        const centerX = this.food.x * this.gridSize + this.gridSize / 2;
        const centerY = this.food.y * this.gridSize + this.gridSize / 2;

        for (let i = 0; i < 8; i++) {
            this.particles.push(Utils.createParticle(centerX, centerY, this.food.color.replace('#', '')));
        }
    }

    levelUp() {
        this.level++;
        this.generateObstacles();
        this.playSound('levelup');

        // 显示升级提示
        this.showLevelUpMessage();
    }

    showLevelUpMessage() {
        // 创建临时升级提示
        const message = document.createElement('div');
        message.textContent = `等级 ${this.level}!`;
        message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #2d3748;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.5em;
            font-weight: bold;
            z-index: 1000;
            animation: levelUpAnimation 2s ease-out forwards;
        `;

        document.body.appendChild(message);
        setTimeout(() => message.remove(), 2000);
    }

    playSound(type) {
        if (!this.soundEnabled) return;

        // 使用 Web Audio API 创建简单音效
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch(type) {
                case 'eat':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1);
                    break;
                case 'levelup':
                    oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1);
                    oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2);
                    break;
                case 'gameover':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);
                    break;
            }

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            // 音频不支持时静默失败
        }
    }
    
    checkCollision() {
        const head = this.snake[0];

        // 检查墙壁碰撞
        if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
            return 'wall';
        }

        // 检查自身碰撞
        for (let i = 1; i < this.snake.length; i++) {
            if (head.x === this.snake[i].x && head.y === this.snake[i].y) {
                return 'self';
            }
        }

        // 检查障碍物碰撞
        for (let obstacle of this.obstacles) {
            if (head.x === obstacle.x && head.y === obstacle.y) {
                return 'obstacle';
            }
        }

        return false;
    }
    
    gameStep() {
        if (!this.gameRunning || this.gamePaused) return;

        this.moveSnake();

        const collision = this.checkCollision();
        if (collision) {
            this.gameOver(collision);
            return;
        }

        this.drawGame();
    }
    
    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.hideOverlay();

        if (this.gameLoop) {
            clearInterval(this.gameLoop);
        }

        // 使用 requestAnimationFrame 替代 setInterval 以获得更流畅的动画
        this.lastTime = 0;
        this.gameLoop = requestAnimationFrame((timestamp) => this.gameLoopRAF(timestamp));

        this.updatePauseButton();
    }

    gameLoopRAF(timestamp) {
        if (!this.gameRunning) return;

        if (timestamp - this.lastTime >= this.gameSpeed) {
            this.gameStep();
            this.lastTime = timestamp;
        }

        if (this.gameRunning) {
            this.gameLoop = requestAnimationFrame((timestamp) => this.gameLoopRAF(timestamp));
        }
    }
    
    togglePause() {
        if (!this.gameRunning) {
            this.startGame();
            return;
        }

        this.gamePaused = !this.gamePaused;
        this.updatePauseButton();

        if (this.gamePaused) {
            this.showOverlay('游戏暂停', '按空格键继续游戏');
            if (this.gameLoop) {
                cancelAnimationFrame(this.gameLoop);
            }
        } else {
            this.hideOverlay();
            this.gameLoop = requestAnimationFrame((timestamp) => this.gameLoopRAF(timestamp));
        }
    }

    restartGame() {
        this.gameRunning = false;
        this.gamePaused = false;

        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
        }

        // 重置游戏状态
        this.snake = [{x: 10, y: 10}];
        this.dx = 0;
        this.dy = 0;
        this.nextDirection = { dx: 0, dy: 0 };
        this.score = 0;
        this.level = 1;
        this.foodEaten = 0;
        this.obstacles = [];
        this.particles = [];
        this.food = this.generateFood();
        this.gameSpeed = GAME_CONFIG.INITIAL_SPEED;

        this.updateDisplay();
        this.drawGame();
        this.showOverlay('开始游戏', '按空格键开始游戏');
        this.updatePauseButton();
    }
    
    gameOver(collisionType) {
        this.gameRunning = false;
        this.gamePaused = false;

        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
        }

        // 播放游戏结束音效
        this.playSound('gameover');

        // 创建爆炸粒子效果
        this.createExplosionParticles();

        // 更新统计数据
        this.gamesPlayed++;
        localStorage.setItem('snakeGamesPlayed', this.gamesPlayed);

        // 更新最高分
        let isNewRecord = false;
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('snakeHighScore', this.highScore);
            isNewRecord = true;
        }

        this.updateDisplay();

        // 显示游戏结束信息
        const collisionMessages = {
            'wall': '撞到了墙壁',
            'self': '撞到了自己',
            'obstacle': '撞到了障碍物'
        };

        const message = isNewRecord ?
            `🎉 新纪录！\n${collisionMessages[collisionType] || '游戏结束'}\n得分: ${this.score}分\n等级: ${this.level}` :
            `${collisionMessages[collisionType] || '游戏结束'}\n得分: ${this.score}分\n等级: ${this.level}\n最高分: ${this.highScore}分`;

        this.showOverlay('游戏结束', message);
        this.updatePauseButton();
    }

    createExplosionParticles() {
        const head = this.snake[0];
        const centerX = head.x * this.gridSize + this.gridSize / 2;
        const centerY = head.y * this.gridSize + this.gridSize / 2;

        for (let i = 0; i < 15; i++) {
            const particle = Utils.createParticle(centerX, centerY, '255, 100, 100');
            particle.vx *= 2;
            particle.vy *= 2;
            particle.life = 60;
            particle.maxLife = 60;
            this.particles.push(particle);
        }
    }
    
    showOverlay(title, message) {
        document.getElementById('overlayTitle').textContent = title;
        document.getElementById('overlayMessage').textContent = message;
        document.getElementById('gameOverlay').classList.remove('hidden');
    }

    hideOverlay() {
        document.getElementById('gameOverlay').classList.add('hidden');
    }

    updateDisplay() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('high-score').textContent = this.highScore;

        // 更新等级显示（如果存在）
        const levelDisplay = document.getElementById('level-display');
        if (levelDisplay) {
            levelDisplay.textContent = this.level;
        }
    }

    updatePauseButton() {
        const pauseButton = document.getElementById('pauseButton');
        if (this.gameRunning && !this.gamePaused) {
            pauseButton.textContent = '暂停';
        } else {
            pauseButton.textContent = '继续';
        }
    }

    // 获取游戏统计信息
    getGameStats() {
        return {
            currentScore: this.score,
            highScore: this.highScore,
            level: this.level,
            foodEaten: this.foodEaten,
            gamesPlayed: this.gamesPlayed,
            snakeLength: this.snake.length
        };
    }
}

// 添加升级动画的CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes levelUpAnimation {
        0% {
            transform: translate(-50%, -50%) scale(0.5);
            opacity: 0;
        }
        50% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 1;
        }
        100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0;
        }
    }

    @keyframes foodPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
`;
document.head.appendChild(style);

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new SnakeGame();
    game.drawGame();

    // 添加键盘快捷键提示
    console.log('🐍 贪吃蛇游戏控制说明:');
    console.log('方向键: 控制蛇的移动');
    console.log('空格键: 暂停/继续游戏');
    console.log('移动端: 滑动屏幕控制方向');
});
