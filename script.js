class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;
        
        // 游戏状态
        this.gameRunning = false;
        this.gamePaused = false;
        this.gameLoop = null;
        
        // 分数
        this.score = 0;
        this.highScore = localStorage.getItem('snakeHighScore') || 0;
        
        // 蛇的初始状态
        this.snake = [
            {x: 10, y: 10}
        ];
        this.dx = 0;
        this.dy = 0;
        
        // 食物
        this.food = this.generateFood();
        
        // 绑定事件
        this.bindEvents();
        this.updateDisplay();
        
        // 游戏速度
        this.gameSpeed = 150;
    }
    
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.togglePause();
            } else if (this.gameRunning && !this.gamePaused) {
                this.changeDirection(e);
            }
        });
        
        // 按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('pauseButton').addEventListener('click', () => {
            this.togglePause();
        });
        
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
    }
    
    changeDirection(event) {
        const keyPressed = event.code;
        const goingUp = this.dy === -1;
        const goingDown = this.dy === 1;
        const goingRight = this.dx === 1;
        const goingLeft = this.dx === -1;
        
        if (keyPressed === 'ArrowLeft' && !goingRight) {
            this.dx = -1;
            this.dy = 0;
        }
        if (keyPressed === 'ArrowUp' && !goingDown) {
            this.dx = 0;
            this.dy = -1;
        }
        if (keyPressed === 'ArrowRight' && !goingLeft) {
            this.dx = 1;
            this.dy = 0;
        }
        if (keyPressed === 'ArrowDown' && !goingUp) {
            this.dx = 0;
            this.dy = 1;
        }
    }
    
    generateFood() {
        return {
            x: Math.floor(Math.random() * this.tileCount),
            y: Math.floor(Math.random() * this.tileCount)
        };
    }
    
    drawGame() {
        // 清空画布
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制蛇
        this.ctx.fillStyle = '#48bb78';
        this.snake.forEach((segment, index) => {
            if (index === 0) {
                // 蛇头用不同颜色
                this.ctx.fillStyle = '#38a169';
            } else {
                this.ctx.fillStyle = '#48bb78';
            }
            this.ctx.fillRect(
                segment.x * this.gridSize + 1,
                segment.y * this.gridSize + 1,
                this.gridSize - 2,
                this.gridSize - 2
            );
        });
        
        // 绘制食物
        this.ctx.fillStyle = '#f56565';
        this.ctx.fillRect(
            this.food.x * this.gridSize + 1,
            this.food.y * this.gridSize + 1,
            this.gridSize - 2,
            this.gridSize - 2
        );
    }
    
    moveSnake() {
        const head = {x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy};
        this.snake.unshift(head);
        
        // 检查是否吃到食物
        if (head.x === this.food.x && head.y === this.food.y) {
            this.score += 10;
            this.food = this.generateFood();
            this.updateDisplay();
            
            // 增加游戏速度
            if (this.gameSpeed > 80) {
                this.gameSpeed -= 2;
            }
        } else {
            this.snake.pop();
        }
    }
    
    checkCollision() {
        const head = this.snake[0];
        
        // 检查墙壁碰撞
        if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
            return true;
        }
        
        // 检查自身碰撞
        for (let i = 1; i < this.snake.length; i++) {
            if (head.x === this.snake[i].x && head.y === this.snake[i].y) {
                return true;
            }
        }
        
        return false;
    }
    
    gameStep() {
        if (!this.gameRunning || this.gamePaused) return;
        
        this.moveSnake();
        
        if (this.checkCollision()) {
            this.gameOver();
            return;
        }
        
        this.drawGame();
    }
    
    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.hideOverlay();
        
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
        }
        
        this.gameLoop = setInterval(() => {
            this.gameStep();
        }, this.gameSpeed);
        
        this.updatePauseButton();
    }
    
    togglePause() {
        if (!this.gameRunning) {
            this.startGame();
            return;
        }
        
        this.gamePaused = !this.gamePaused;
        this.updatePauseButton();
        
        if (this.gamePaused) {
            this.showOverlay('游戏暂停', '按空格键继续游戏');
        } else {
            this.hideOverlay();
        }
    }
    
    restartGame() {
        this.gameRunning = false;
        this.gamePaused = false;
        
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
        }
        
        // 重置游戏状态
        this.snake = [{x: 10, y: 10}];
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.food = this.generateFood();
        this.gameSpeed = 150;
        
        this.updateDisplay();
        this.drawGame();
        this.showOverlay('开始游戏', '按空格键开始游戏');
        this.updatePauseButton();
    }
    
    gameOver() {
        this.gameRunning = false;
        this.gamePaused = false;
        
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
        }
        
        // 更新最高分
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('snakeHighScore', this.highScore);
            this.updateDisplay();
        }
        
        this.showOverlay('游戏结束', `得分: ${this.score}分\n按空格键重新开始`);
        this.updatePauseButton();
    }
    
    showOverlay(title, message) {
        document.getElementById('overlayTitle').textContent = title;
        document.getElementById('overlayMessage').textContent = message;
        document.getElementById('gameOverlay').classList.remove('hidden');
    }
    
    hideOverlay() {
        document.getElementById('gameOverlay').classList.add('hidden');
    }
    
    updateDisplay() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('high-score').textContent = this.highScore;
    }
    
    updatePauseButton() {
        const pauseButton = document.getElementById('pauseButton');
        if (this.gameRunning && !this.gamePaused) {
            pauseButton.textContent = '暂停';
        } else {
            pauseButton.textContent = '继续';
        }
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new SnakeGame();
    game.drawGame();
});
