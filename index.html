<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <header>
            <h1>🐍 贪吃蛇游戏</h1>
            <div class="score-board">
                <div class="score">
                    <span>分数: </span>
                    <span id="current-score">0</span>
                </div>
                <div class="high-score">
                    <span>最高分: </span>
                    <span id="high-score">0</span>
                </div>
                <div class="level">
                    <span>等级: </span>
                    <span id="level-display">1</span>
                </div>
            </div>
        </header>

        <div class="game-area">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
            <div class="game-overlay" id="gameOverlay">
                <div class="overlay-content">
                    <h2 id="overlayTitle">开始游戏</h2>
                    <p id="overlayMessage">按空格键开始游戏</p>
                    <button id="startButton" class="game-button">开始游戏</button>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-buttons">
                <button id="pauseButton" class="game-button">暂停</button>
                <button id="restartButton" class="game-button">重新开始</button>
            </div>
            <div class="instructions">
                <h3>游戏说明</h3>
                <ul>
                    <li>使用方向键或滑动屏幕控制蛇的移动</li>
                    <li>🍎 红色食物: +10分</li>
                    <li>⭐ 金色食物: +25分</li>
                    <li>💎 紫色食物: +50分</li>
                    <li>每5个食物升一级，会出现障碍物</li>
                    <li>不要撞到墙壁、自己的身体或障碍物</li>
                    <li>按空格键暂停/继续游戏</li>
                    <li>游戏速度会随着分数增加而提升</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
