* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    width: 100%;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #4a5568;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.score-board {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 10px;
}

.score, .high-score, .level {
    font-size: 1.2em;
    font-weight: bold;
    color: #2d3748;
    min-width: 120px;
}

.score span:last-child, .high-score span:last-child {
    color: #38a169;
}

.level span:last-child {
    color: #9f7aea;
}

.game-area {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

#gameCanvas {
    border: 4px solid #4a5568;
    border-radius: 10px;
    background: #2d3748;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    transition: opacity 0.3s ease;
}

.game-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-content h2 {
    font-size: 2em;
    margin-bottom: 15px;
    color: #ffd700;
}

.overlay-content p {
    font-size: 1.1em;
    margin-bottom: 20px;
}

.game-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 1em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
    font-weight: bold;
}

.game-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.game-button:active {
    transform: translateY(0);
}

.controls {
    margin-top: 20px;
}

.control-buttons {
    margin-bottom: 25px;
}

.instructions {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    text-align: left;
}

.instructions h3 {
    color: #2d3748;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.3em;
}

.instructions ul {
    list-style: none;
    color: #4a5568;
}

.instructions li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.instructions li:before {
    content: "🎮";
    position: absolute;
    left: 0;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .game-container {
        padding: 20px;
        margin: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 350px;
        height: auto;
    }
    
    .score-board {
        flex-direction: column;
        gap: 10px;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
}

@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #ffd700; }
    100% { transform: scale(1); }
}

.game-button:hover {
    animation: pulse 0.5s ease-in-out;
}

#gameCanvas {
    transition: all 0.3s ease;
}

#gameCanvas:hover {
    animation: glow 2s ease-in-out infinite;
}

.score span:last-child,
.high-score span:last-child,
.level span:last-child {
    transition: all 0.3s ease;
}

.score-update {
    animation: scoreUpdate 0.5s ease-out;
}

/* 移动端优化 */
@media (max-width: 600px) {
    .score-board {
        flex-direction: column;
        text-align: center;
    }

    .score, .high-score, .level {
        min-width: auto;
        margin-bottom: 5px;
    }

    .instructions li {
        font-size: 0.9em;
    }

    .game-button {
        padding: 10px 20px;
        font-size: 0.9em;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .game-button:hover {
        animation: none;
        transform: none;
    }

    .game-button:active {
        transform: scale(0.95);
        background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
    }
}
